#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Azure Search 解析的专用脚本
"""

import json
import os
from azure_general_parser import AzurePricingParser


def test_search_parsing():
    """测试 Azure Search 的解析"""
    html_file = "prod-html/search-index.html"
    
    print("🔍 测试 Azure Search 解析")
    print(f"文件: {html_file}")
    
    if not os.path.exists(html_file):
        print(f"❌ 文件不存在: {html_file}")
        return
    
    try:
        # 读取HTML文件
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 创建解析器
        parser = AzurePricingParser(
            html_content=html_content,
            product_type="auto",  # 让它自动检测
            config_file_path="soft-category.json",
            include_region_info=True,
            region_info_mode="hybrid"
        )
        
        # 执行解析
        results = parser.parse_all()
        
        # 保存结果
        output_file = "output/search_test.json"
        os.makedirs("output", exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 解析完成，结果保存到: {output_file}")
        
        # 分析结果
        print(f"\n📊 解析结果分析:")
        print(f"检测到的产品类型: {results['extraction_metadata']['product_type']}")
        print(f"产品名称: {results['product_info']['product_name']}")
        print(f"产品描述长度: {len(results['product_info']['description'])} 字符")
        
        # 检查定价表
        pricing_tables = results['pricing_tables']
        print(f"\n📋 定价表信息:")
        print(f"定价表数量: {len(pricing_tables)}")
        
        for i, table in enumerate(pricing_tables):
            print(f"  表格 {i+1}:")
            print(f"    ID: {table['table_id']}")
            print(f"    类型: {table['table_type']}")
            print(f"    标题: {table['title']}")
            print(f"    行数: {len(table['rows'])}")
            
            # 显示前几行数据
            if table['rows']:
                print(f"    示例数据:")
                for j, row in enumerate(table['rows'][:3]):
                    print(f"      行 {j+1}: {row['instance_name']}")
                    if row['price_hourly'] > 0:
                        print(f"        小时价格: ￥{row['price_hourly']}")
                    if row['price_monthly'] > 0:
                        print(f"        月度价格: ￥{row['price_monthly']}")
                    if row['memory']:
                        print(f"        描述: {row['memory'][:50]}...")
        
        # 检查服务层级
        service_tiers = results['service_tiers']
        print(f"\n🏗️ 服务层级:")
        print(f"层级数量: {len(service_tiers)}")
        for tier in service_tiers:
            print(f"  - {tier['tier_name']} (级别: {tier['tier_level']})")
            if tier.get('description'):
                print(f"    描述: {tier['description'][:100]}...")
        
        # 检查区域信息
        regions = results['regions']
        print(f"\n🌍 区域信息:")
        print(f"区域数量: {len(regions)}")
        active_regions = [r for r in regions if r['is_active']]
        if active_regions:
            print(f"激活区域: {active_regions[0]['region_name']}")
        
        # 检查FAQ
        faqs = results['faqs']
        print(f"\n❓ FAQ信息:")
        print(f"FAQ数量: {len(faqs)}")
        if faqs:
            print(f"示例FAQ: {faqs[0]['question'][:50]}...")
        
        # 检查定价规则
        pricing_rules = results['pricing_rules']
        print(f"\n📜 定价规则:")
        print(f"规则数量: {len(pricing_rules)}")
        for rule_name, rule_info in pricing_rules.items():
            print(f"  - {rule_name}: {rule_info.get('description', '')[:50]}...")
        
        # 检查区域过滤
        filter_info = results['region_filter_info']
        print(f"\n🔧 区域过滤信息:")
        print(f"过滤的表格数: {filter_info['total_filtered']}")
        print(f"当前激活区域: {filter_info['active_region']}")
        
        return results
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    print("🚀 开始测试 Azure Search 解析")
    
    results = test_search_parsing()
    
    if results:
        print(f"\n✅ 测试完成")
        print("请检查 output/search_test.json 文件查看详细结果")
    else:
        print(f"\n❌ 测试失败")


if __name__ == "__main__":
    main()
