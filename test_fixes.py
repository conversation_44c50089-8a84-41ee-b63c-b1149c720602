#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
验证以下问题是否已修复：
1. 产品信息描述缺失
2. regions 数据重复
3. PostgreSQL 价格解析问题
4. SQL Database 复杂层级结构
"""

import json
import os
from pathlib import Path
from azure_general_parser import AzurePricingParser


def test_single_product(html_file: str, product_type: str = "auto"):
    """测试单个产品的解析"""
    print(f"\n{'='*60}")
    print(f"测试产品: {html_file}")
    print(f"{'='*60}")
    
    if not os.path.exists(html_file):
        print(f"❌ 文件不存在: {html_file}")
        return None
    
    try:
        # 读取HTML文件
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 创建解析器
        parser = AzurePricingParser(
            html_content=html_content,
            product_type=product_type,
            config_file_path="soft-category.json",
            include_region_info=True,
            region_info_mode="hybrid"
        )
        
        # 执行解析
        results = parser.parse_all()
        
        # 验证修复效果
        print(f"\n📊 解析结果摘要:")
        print(f"产品类型: {results['extraction_metadata']['product_type']}")
        print(f"产品名称: {results['product_info']['product_name']}")
        print(f"产品描述长度: {len(results['product_info']['description'])} 字符")
        
        # 检查区域重复问题
        regions = results['regions']
        region_ids = [r['region_id'] for r in regions]
        unique_region_ids = set(region_ids)
        print(f"区域总数: {len(regions)}")
        print(f"唯一区域数: {len(unique_region_ids)}")
        if len(regions) != len(unique_region_ids):
            print("❌ 区域数据仍有重复")
        else:
            print("✅ 区域数据无重复")
        
        # 检查定价表
        pricing_tables = results['pricing_tables']
        print(f"定价表数量: {len(pricing_tables)}")
        
        # 检查价格解析
        total_rows = sum(len(table['rows']) for table in pricing_tables)
        rows_with_price = 0
        for table in pricing_tables:
            for row in table['rows']:
                if row['price_hourly'] > 0 or row['price_monthly'] > 0:
                    rows_with_price += 1
        
        print(f"总定价行数: {total_rows}")
        print(f"有价格的行数: {rows_with_price}")
        if total_rows > 0:
            price_ratio = rows_with_price / total_rows
            print(f"价格解析率: {price_ratio:.2%}")
            if price_ratio < 0.3:
                print("⚠️ 价格解析率较低")
            else:
                print("✅ 价格解析正常")
        
        # 检查服务层级
        service_tiers = results['service_tiers']
        print(f"服务层级数量: {len(service_tiers)}")
        
        # 检查多层级结构
        max_level = 0
        for tier in service_tiers:
            max_level = max(max_level, tier['tier_level'])
            if 'sub_tiers' in tier and tier['sub_tiers']:
                for sub_tier in tier['sub_tiers']:
                    max_level = max(max_level, sub_tier['tier_level'])
                    if 'sub_tiers' in sub_tier and sub_tier['sub_tiers']:
                        for sub_sub_tier in sub_tier['sub_tiers']:
                            max_level = max(max_level, sub_sub_tier['tier_level'])
        
        print(f"最大层级深度: {max_level}")
        
        # 检查区域过滤
        filter_info = results['region_filter_info']
        print(f"过滤的表格数: {filter_info['total_filtered']}")
        print(f"当前激活区域: {filter_info['active_region']}")
        
        return results
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_specific_issues():
    """测试特定问题的修复"""
    print("🔍 测试特定问题修复效果")
    
    test_cases = [
        {
            'file': 'prod-html/postgresql-index.html',
            'name': 'PostgreSQL 价格解析',
            'checks': ['price_parsing', 'storage_tables']
        },
        {
            'file': 'prod-html/sql-database-index.html', 
            'name': 'SQL Database 层级结构',
            'checks': ['multi_level_tiers']
        },
        {
            'file': 'prod-html/mysql-index.html',
            'name': 'MySQL 区域重复',
            'checks': ['region_deduplication']
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 测试: {test_case['name']}")
        results = test_single_product(test_case['file'])
        
        if not results:
            continue
            
        # 执行特定检查
        for check in test_case['checks']:
            if check == 'price_parsing':
                # 检查价格解析
                pricing_tables = results['pricing_tables']
                total_rows = sum(len(table['rows']) for table in pricing_tables)
                rows_with_price = sum(1 for table in pricing_tables 
                                    for row in table['rows'] 
                                    if row['price_hourly'] > 0 or row['price_monthly'] > 0)
                
                if total_rows > 0 and rows_with_price / total_rows > 0.3:
                    print(f"  ✅ 价格解析: {rows_with_price}/{total_rows} ({rows_with_price/total_rows:.1%})")
                else:
                    print(f"  ❌ 价格解析率低: {rows_with_price}/{total_rows}")
                    
            elif check == 'storage_tables':
                # 检查存储表格
                storage_tables = [t for t in results['pricing_tables'] if t['table_type'] == 'storage']
                print(f"  📦 存储表格数量: {len(storage_tables)}")
                
            elif check == 'multi_level_tiers':
                # 检查多层级结构
                max_level = 0
                for tier in results['service_tiers']:
                    max_level = max(max_level, tier['tier_level'])
                    if 'sub_tiers' in tier:
                        for sub_tier in tier['sub_tiers']:
                            max_level = max(max_level, sub_tier['tier_level'])
                            if 'sub_tiers' in sub_tier:
                                for sub_sub_tier in sub_tier['sub_tiers']:
                                    max_level = max(max_level, sub_sub_tier['tier_level'])
                
                if max_level >= 3:
                    print(f"  ✅ 多层级结构: {max_level} 层")
                else:
                    print(f"  ⚠️ 层级深度: {max_level} 层")
                    
            elif check == 'region_deduplication':
                # 检查区域去重
                regions = results['regions']
                region_ids = [r['region_id'] for r in regions]
                unique_count = len(set(region_ids))
                
                if len(regions) == unique_count:
                    print(f"  ✅ 区域去重: {unique_count} 个唯一区域")
                else:
                    print(f"  ❌ 区域重复: {len(regions)} 总数, {unique_count} 唯一")


def main():
    """主函数"""
    print("🚀 开始测试修复效果")
    
    # 测试特定问题
    test_specific_issues()
    
    print(f"\n{'='*60}")
    print("📋 测试完成")
    print("请检查上述输出以验证修复效果")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
