#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的批量处理脚本，用于测试修复效果
"""

import os
import json
from pathlib import Path
from azure_general_parser import AzurePricingParser


def process_file(html_file: Path, output_dir: Path):
    """处理单个HTML文件"""
    print(f"\n处理文件: {html_file.name}")
    
    try:
        # 读取HTML内容
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 创建解析器
        parser = AzurePricingParser(
            html_content=html_content,
            product_type="auto",
            config_file_path="soft-category.json",
            include_region_info=True,
            region_info_mode="hybrid"
        )
        
        # 执行解析
        results = parser.parse_all()
        
        # 生成输出文件名
        detected_product = results['extraction_metadata']['product_type']
        output_filename = f"{detected_product}_fixed.json"
        output_path = output_dir / output_filename
        
        # 保存结果
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print(f"✅ 成功: {output_filename}")
        print(f"   产品类型: {detected_product}")
        print(f"   区域数: {len(results['regions'])}")
        print(f"   表格数: {len(results['pricing_tables'])}")
        print(f"   过滤表格数: {results['region_filter_info']['total_filtered']}")
        
        # 检查区域重复
        region_ids = [r['region_id'] for r in results['regions']]
        unique_regions = len(set(region_ids))
        if len(region_ids) != unique_regions:
            print(f"   ⚠️ 区域重复: {len(region_ids)} -> {unique_regions}")
        
        # 检查价格解析率
        total_rows = sum(len(table['rows']) for table in results['pricing_tables'])
        rows_with_price = sum(1 for table in results['pricing_tables'] 
                            for row in table['rows'] 
                            if row['price_hourly'] > 0 or row['price_monthly'] > 0)
        
        if total_rows > 0:
            price_ratio = rows_with_price / total_rows
            print(f"   价格解析率: {price_ratio:.1%} ({rows_with_price}/{total_rows})")
        
        return True
        
    except Exception as e:
        print(f"❌ 失败: {e}")
        return False


def main():
    """主函数"""
    input_dir = Path("prod-html")
    output_dir = Path("output")
    
    # 确保输出目录存在
    output_dir.mkdir(exist_ok=True)
    
    print("🚀 开始批量处理（测试修复效果）")
    
    # 获取HTML文件
    html_files = list(input_dir.glob("*.html"))
    html_files.sort()
    
    print(f"发现 {len(html_files)} 个HTML文件")
    
    success_count = 0
    
    # 处理每个文件
    for html_file in html_files:
        if process_file(html_file, output_dir):
            success_count += 1
    
    print(f"\n📊 处理完成")
    print(f"成功: {success_count}/{len(html_files)}")
    print(f"输出目录: {output_dir}")


if __name__ == "__main__":
    main()
